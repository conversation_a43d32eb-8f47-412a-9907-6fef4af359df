'use client'

import React from 'react'
import { DependencyGrid } from '@/components/dependencies/DependencyGrid'

export default function DependenciasPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-chia-blue-50 to-chia-green-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Explora por Dependencias
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Navega por las diferentes secretarías y dependencias municipales para encontrar 
            los trámites y servicios que necesitas de manera rápida y organizada.
          </p>
        </div>

        {/* Dependency Grid with Enhanced UX */}
        <DependencyGrid 
          showSearch={true}
          showStats={true}
          className="max-w-7xl mx-auto"
        />
      </div>
    </div>
  )
}
